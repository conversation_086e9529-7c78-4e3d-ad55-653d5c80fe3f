.ctn{
    width: 100%;
    height: 100vh;
    background: #ffffff;
    position: relative;

    .reactflowWrapper {
        width: 100%;
        height: 100%;
    }

    // 隐藏 react-flow 右下角的 logo
    //:global(.react-flow__attribution) {
    //    display: none !important;
    //}

    // 工具面板触发器组样式
    .toolTriggerGroup {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 12px;
        z-index: 1000;
    }

    // 工具面板触发器样式
    .toolTrigger {
        background: #1890ff;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        transition: all 0.3s ease;
        user-select: none;
        font-size: 14px;
        font-weight: 500;

        &:hover {
            background: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        }

        &.active {
            background: #096dd9;
            transform: translateY(-1px);
        }

        span {
            font-size: 14px;
        }
    }

    // 布局优化按钮样式
    .layoutTrigger {
        background: #52c41a;
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        transition: all 0.3s ease;
        user-select: none;
        font-size: 14px;
        font-weight: 500;

        &:hover:not(.disabled) {
            background: #73d13d;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
        }

        &.disabled {
            background: #d9d9d9;
            color: #999;
            cursor: not-allowed;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        span {
            font-size: 14px;
        }
    }



    // 工具面板容器样式
    .toolPanelContainer {
        position: fixed;
        bottom: 110px; // 增加底部距离，为按钮组留出空间
        left: 50%;
        transform: translateX(-50%);
        width: 35%;
        max-width: 600px;
        min-width: 400px;
        max-height: 70vh;
        overflow-y: auto;
        z-index: 999;
        animation: slideUp 0.3s ease-out;
    }

    // 节点工具面板容器样式
    .nodeToolPanelContainer {
        position: absolute;
        overflow-y: auto;
        z-index: 1000;
        animation: fadeIn 0.2s ease-out;
        transform: none; // 不使用transform偏移，直接使用计算好的位置
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15); // 增强阴影效果，提高可见性

        // 滑入动画
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        // 淡入动画
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        // 自定义滚动条（应用于两个面板）
        &::-webkit-scrollbar,
        .nodeToolPanelContainer::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track,
        .nodeToolPanelContainer::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb,
        .nodeToolPanelContainer::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
                background: #a8a8a8;
            }
        }
    }

    // 响应式设计
    @media (max-width: 1200px) {
        .toolPanelContainer {
            width: 45%;
            min-width: 350px;
        }

        .nodeToolPanelContainer {
            // 响应式尺寸通过 JavaScript 动态设置
        }
    }

    @media (max-width: 768px) {
        .toolPanelContainer {
            width: 90%;
            min-width: 300px;
            left: 5%;
            transform: none;
            bottom: 90px; // 调整移动端的底部距离
        }

        .nodeToolPanelContainer {
            // 响应式尺寸通过 JavaScript 动态设置
        }

        .toolTriggerGroup {
            bottom: 20px;
            flex-direction: column;
            gap: 8px;
        }

        .toolTrigger, .layoutTrigger {
            padding: 10px 16px;
            font-size: 13px;

            span {
                font-size: 13px;
            }
        }


    }
}
